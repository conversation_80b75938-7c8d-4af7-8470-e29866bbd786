<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadRawFile } from 'element-plus'
import { ElMessage, ElMessageBox, ElUpload, genFileId } from 'element-plus'
import { getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'

const instance = getCurrentInstance()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

const closeModal = () => {
  console.log('[Page] Calling close modal')
  instance?.proxy?.$closeWaitingModal?.()
}

const { t } = useI18n()

// 升级方式 0-自动升级 1-手动升级
const frequencyBand = ref(0)
const autoUpdate = ref(false)
const resetAfterUpdate = ref(false)

const firmwareList = ref()
const firmwareUploader = ref()

const firmwareChange = (uploadFile: UploadFile) => {
  firmwareList.value = [uploadFile]
}

const handleFirmwareExceed = (files: UploadFiles) => {
  firmwareUploader.value!.clearFiles()

  const file = files[0] as UploadRawFile

  file.uid = genFileId()
  firmwareUploader.value!.handleStart(file)
}

// 固件升级
const updateByFirmware = () => {
  if (!firmwareList.value || firmwareList.value.length === 0) {
    ElMessage.error(t('SystemConfig.Update.PleaseUploadFirmware'))

    return
  }
  if (resetAfterUpdate.value)
    getWaittingModal(3, 'http://192.168.10.1')

  else
    getWaittingModal(3)

  const file = firmwareList.value[0]
  const reader = new FileReader()

  reader.onload = (event: any) => {
    const firmwareFile = event.target.result.split(',')
    const fileData = firmwareFile[1]

    $api('', {
      requestType: 301,
      data: {
        fileName: file.fileName,
        fileSize: file.size.toString(),
        reset: resetAfterUpdate.value ? '1' : '0',
        file: fileData,
      },
    }).then(data => {
      if (data.err_code == -15) {

      }
      else if (data.err_code == -9) {
        console.log('111111')
        closeModal()
        window.location.reload()
        sessionStorage.setItem('captcha', undefined)
      }
      else if (data.err_code == -2) {
        console.log('22222')
        closeModal()
      }
      else if (data.err_code == -19) {
        console.log('333333')
        closeModal()
      }
      else {
        console.log('44444')
        closeModal()
      }
    })
  }
  reader.readAsDataURL(file.raw)
}

const had_download_ok = ref(false)
const download_step = ref(0)
const show_download_progress = ref(false)

//  执行升级请求
const updatePack = () => {
  $api('', {
    requestType: 234,
    data: { },
  }).then(res => {
    if (res.fw_info.fw_status == '4') {
      $api('', {
        requestType: 232,
        data: {
          reset: resetAfterUpdate.value ? '1' : '0',
        },
      }).then(data => {
        if (data.err_code == -15) {

        }
        else if (data.err_code == -9) {
          console.log('55555')
          closeModal()
          window.location.reload()
          sessionStorage.setItem('captcha', undefined)
        }
        else if (data.err_code == -2) {
          console.log('66666')
          closeModal()
        }
        else if (data.err_code == -19) {
          console.log('777777')
          closeModal()
        }
        else {
          console.log('888888')
          closeModal()
        }
      })
    }
    else {
      setTimeout(updatePack(), 1000)
    }
  })
}

// 正常下载完成，无自动更新提醒用户是否继续下载
const do_sysupgrade_online = () => {
  ElMessageBox.confirm(t('SystemConfig.Update.UpgradeConfirmation'), t('SystemConfig.Reset.Warning'), {
    confirmButtonText: t('SystemConfig.Update.Confirm'),
    cancelButtonText: t('SystemConfig.Update.Cancel'),
    type: 'warning',
  }).then(() => {
    download_fw()
  })
}

//  下载文件
const download_fw = () => {
  $api('', {
    requestType: 231,
  }).then(data => {
    if (data.err_code == 0) {
      download_step.value = 0
      show_download_progress.value = true
      had_download_ok.value = false

      get_fw_download_step()

      // check_download_status()

      if (data.fw_info.fw_status != undefined && data.fw_info.fw_status != '') {
        if (data.fw_info.fw_status == '4')
          had_download_ok.value = true
        else
          had_download_ok.value = false
      }
    }
    else {
      had_download_ok.value = false
      ElMessage.error(t('SystemConfig.Update.DownloadFailed'))
    }
  })
}

let error_server_time = 0
let error_md5_time_time = 0

const get_fw_server_stat = () => {
  $api('', {
    requestType: 235,
  }).then(data => {
    if (data.fw_info.fw_stat == '1')
      error_server_time++
    if (data.fw_info.fw_stat == '5')
      error_md5_time_time++

    if (error_server_time >= 5) {
      // stop_server_stat = 1;
      ElMessage.error(t('SystemConfig.Update.ServerConnectionFailed'))
      error_server_time = 0
    }
    if (error_md5_time_time >= 5) {
      // stop_server_stat = 1;
      ElMessage.error(t('SystemConfig.Update.FileVerificationError'))
      error_md5_time_time = 0
    }
  })
}

//  查询下载进度，更新下载进度
const get_fw_download_step = () => {
  $api('', {
    requestType: 233,
  }).then(data => {
    console.log('get_fw_download_step', data)
    if (data.fw_info.fw_step != '')
      download_step.value = data.fw_info.fw_step
    else
      download_step.value = 0

    // 下载未完成就继续请求，请求完成修改下载状态
    if (download_step.value < 100) {
      setTimeout(() => {
        get_fw_download_step()
      }, 1000)
    }
    else {
      had_download_ok.value = true

      // 下载完成后根基是否自动升级执行升级请求
      if (autoUpdate.value) {
        if (resetAfterUpdate.value)
          getWaittingModal(3, 'http://192.168.10.1')

        else
          getWaittingModal(3)
        updatePack()
      }
    }
  })
}

// 235 fw_stat 0-normal 1-timeout 2-normal 5-md5 error
// 234 fw_status 0-downloading 4-download ok
// 233 fw_step
// 231 fw_download

const check_download_status = () => {
  $api('', {
    requestType: 234,
  }).then(res => {
    console.log('check_download_status', res)
    if (res.err_code == 0) {
      if (res.fw_info.fw_status == '4' && had_download_ok.value) {
        // do_sysupgrade_online()
      }
      else {
        if (!had_download_ok.value) {
          setTimeout(() => {
            check_download_status()
          }, 1000)
        }
      }
    }
  })
}

// 自动升级
const autoUpdateFirmware = () => {
  //  版本一致，提示已经是最新版本
  if (frameworkInfo.value.current_version == frameworkInfo.value.version) {
    ElMessage.success(t('SystemConfig.Update.AlreadyLatestVersion'))

    return
  }
  if (had_download_ok.value) {
    // 根据下载完成状态判断
    // 是-> 重新下载
    // 开始更新-> 直接升级
    ElMessageBox.confirm(t('SystemConfig.Update.FirmwareDownloaded'), t('SystemConfig.Reset.Warning'), {
      confirmButtonText: t('SystemConfig.Update.Yes'),
      cancelButtonText: t('SystemConfig.Update.UploadFirmware'),
      type: 'warning',
      distinguishCancelAndClose: true,
    }).then(() => {
      download_fw()
    }).catch(action => {
      if (action === 'close') {
      }
      else if (action === 'cancel') {
        if (resetAfterUpdate.value)
          getWaittingModal(3, 'http://192.168.10.1')

        else
          getWaittingModal(3)
        updatePack()
      }
    })
  }
  else {
    do_sysupgrade_online()
  }
}

const frameworkInfo = ref({
  current_version: '',
  fw_file_name: '',
  fw_status: '',
  version: '',
})

const getFwInfo = () => {
  $api('', {
    requestType: 230,
  }).then(data => {
    if (data.err_code == 0) {
      frameworkInfo.value.current_version = data.fw_info.current_version
      frameworkInfo.value.fw_file_name = data.fw_info.fw_file_name
      frameworkInfo.value.fw_status = data.fw_info.fw_status
      frameworkInfo.value.version = data.fw_info.version

      // 检测为下载完成展示进度条，下载百分比是100,设置标志是否下载完成
      if (frameworkInfo.value.fw_status == '4') {
        had_download_ok.value = true
        show_download_progress.value = true
        download_step.value = 100
      }
      else {
        had_download_ok.value = false
      }
    }
    else {
      ElMessage.error(t('SystemConfig.Update.GetFirmwareInfoFailed'))
    }
  })
}

onMounted(() => {
  getFwInfo()
})

// 备份
const fileList = ref()
const uploader = ref()

const handleChange = (uploadFile: UploadFile) => {
  fileList.value = [uploadFile]
}

const handleExceed = (files: UploadFiles) => {
  uploader.value!.clearFiles()

  const file = files[0] as UploadRawFile

  file.uid = genFileId()
  uploader.value!.handleStart(file)
}

const backUp = () => {
  $api('', {
    requestType: 303,
  }).then(data => {
    if (data.err_code == 0) {
      const fileUrl = data.info.fileUrl
      const fileName = data.info.fileName
      const backupUrl = `http://${window.location.host}${fileUrl}`
      const a = document.createElement('a')

      a.href = backupUrl
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  })
}

const importBackUp = () => {
  const file = fileList.value[0]
  const reader = new FileReader()

  reader.onload = (event: any) => {
    const backupFileData = event.target.result.split(',')
    const fileData = backupFileData[1]

    $api('', {
      requestType: 302,
      data: {
        fileName: file.name,
        fileSize: file.size.toString(),
        file: fileData,
      },
    }).then(data => {
      if (data.err_code == 0)
        ElMessage.success('导入成功')
    })
  }
  reader.readAsDataURL(file.raw)
}
</script>

<template>
  <VCard
    class="mb-4"
    :title="t('SystemConfig.Update.FirmwareUpgrade')"
  >
    <VCardText>
      <div class="mb-4 w-full border-fill-primary rounded d-flex">
        <div
          class="flex-1-0 rounded-e-0 text-center py-2 cursor-pointer text-primary font-weight-medium"
          :class="[frequencyBand === 0 ? 'bg-primary-semi-transparent' : '']"
          @click="frequencyBand = 0"
        >
          {{ t('SystemConfig.Update.AutoUpgrade') }}
        </div>
        <div
          class="h-full bg-primary"
          style="flex-shrink: 0;inline-size: 1px;"
        />
        <div
          class="flex-1-0 rounded-s-0 text-center py-2 cursor-pointer text-primary font-weight-medium"
          :class="[frequencyBand === 1 ? 'bg-primary-semi-transparent' : '']"
          @click="frequencyBand = 1"
        >
          {{ t('SystemConfig.Update.ManualUpgrade') }}
        </div>
      </div>

      <!-- 自动更新 -->
      <div v-show="frequencyBand == 0">
        <VRow class="match-height pa-0 my-4">
          <VCol
            class="py-0"
            cols="12"
          >
            <VRow class="match-height">
              <VCol md="3">
                <div class="mb-2">
                  {{ t('SystemConfig.Update.CurrentVersion') }}
                </div>
                <div class="text-h6">
                  {{ frameworkInfo.current_version || '--' }}
                </div>
              </VCol>
              <VCol md="3">
                <div class="mb-2">
                  {{ t('SystemConfig.Update.LatestVersion') }}
                </div>
                <div class="text-h6 text-success">
                  {{ frameworkInfo.version || '--' }}
                </div>
              </VCol>
              <VCol md="6">
                <div class="mb-2">
                  {{ t('ServerFileName') }}
                </div>
                <div class="text-h6">
                  {{ frameworkInfo.fw_file_name || '--' }}
                </div>
              </VCol>
            </vRow>
          </VCol>
        </vRow>

        <div
          v-if="show_download_progress"
          class="mb-4"
        >
          <div class="text-h6 mb-2">
            {{ t('Download') }}
          </div>
          <div class="d-flex align-center">
            <VProgressLinear
              :model-value="download_step"
              color="primary"
            />
            <span class="text-subtitle-1 ml-2">{{ download_step }}%</span>
          </div>
        </div>

        <div class="mb-2 pa-4 rounded border d-flex justify-space-between align-center">
          <div>
            <div class="mb-1 text-h6">
              {{ t('SystemConfig.Update.RestartAfterUpgrade') }}
            </div>
            <div class="text-subtitle-2">
              {{ t('SystemConfig.Update.RestartAfterUpgradeDesc') }}
            </div>
          </div>
          <VSwitch v-model="autoUpdate" />
        </div>
        <div class="mb-2 pa-4 rounded border d-flex justify-space-between align-center">
          <div>
            <div class="mb-1 text-h6">
              {{ t('SystemConfig.Reset.FactoryReset') }}
            </div>
            <div class="text-subtitle-2">
              {{ t('SystemConfig.Reset.ResetWarning') }}
            </div>
          </div>
          <VSwitch v-model="resetAfterUpdate" />
        </div>
        <div
          class="mt-4"
          style="display: flex;justify-content: flex-end;"
        >
          <VBtn
            color="primary"
            @click="autoUpdateFirmware"
          >
            <span v-if="show_download_progress == false">{{ t('SystemConfig.Update.UpgradeConfirmation') }}</span>
            <template v-if="show_download_progress == true">
              <span v-if="download_step == 100">{{ t('SystemConfig.Update.ClickUpdate') }}</span>
              <span v-else>{{ t('SystemConfig.Update.DownloadIng') }}</span>
            </template>
          </VBtn>
        </div>
      </div>
      <!-- 手动更新 -->
      <div v-show="frequencyBand == 1">
        <div class="pa-2 mb-4 rounded border bg-secondary-opacity d-flex align-start">
          <VIcon
            class="mr-2"
            icon="tabler-info-circle"
            color="secondary"
          />
          <div class="text-subtitle-1 text-secondary">
            {{ t('SystemConfig.Update.Desc') }}
          </div>
        </div>
        <div class="text-h5 mb-2">
          {{ t('SystemConfig.Update.SelectFirmwareFile') }}
        </div>
        <div class="mb-4">
          <ElUpload
            ref="firmwareUploader"
            :multiple="false"
            :on-change="firmwareChange"
            :auto-upload="false"
            :show-file-list="false"
            :limit="1"
            :on-exceed="handleFirmwareExceed"
          >
            <VBtn
              color="secondary"
              variant="tonal"
            >
              {{ t('SystemConfig.Update.SelectFile') }}
            </VBtn>
            <span class="ml-4 text-secondary">{{ firmwareList && firmwareList.length ? firmwareList[0].name : t('SystemConfig.Update.NoFileSelected') }}</span>
          </ElUpload>
        </div>
        <div class="mb-2 pa-4 rounded border d-flex justify-space-between align-center">
          <div>
            <div class="mb-1 text-h6">
              {{ t('SystemConfig.Reset.FactoryReset') }}
            </div>
            <div class="text-subtitle-2">
              {{ t('SystemConfig.Reset.ResetWarning') }}
            </div>
          </div>
          <VSwitch v-model="resetAfterUpdate" />
        </div>
        <div
          class="mt-4"
          style="display: flex;justify-content: flex-end;"
        >
          <VBtn
            color="primary"
            @click="updateByFirmware"
          >
            {{ t('SystemConfig.Update.UploadFirmware') }}
          </VBtn>
        </div>
      </div>
    </VCardText>
  </VCard>

  <!-- 系统备份与恢复 -->
  <VCard
    class="mb-5"
    :title="t('SystemConfig.Update.BackupRestore')"
  >
    <VCardText class="pt-0">
      <div class="pa-2 mb-2 rounded border bg-secondary-opacity d-flex align-start">
        <VIcon
          class="mr-2"
          icon="tabler-info-circle"
          color="secondary"
        />
        <div class="text-subtitle-1 text-secondary">
          {{ t('SystemConfig.Update.BackupRestoreDesc') }}
        </div>
      </div>
      <div class="text-h5">
        {{ t('SystemConfig.Update.ImportBackup') }}
      </div>
      <div
        class="mt-4"
        style="display: flex;justify-content: flex-end;"
      >
        <ElUpload
          ref="uploader"
          :multiple="false"
          :on-change="handleChange"
          :auto-upload="false"
          :show-file-list="false"
          :limit="1"
          :on-exceed="handleExceed"
        >
          <VBtn
            color="secondary"
            variant="tonal"
          >
            {{ t('SystemConfig.Update.SelectFile') }}
          </VBtn>
          <span class="ml-4 text-secondary">{{ fileList && fileList.length ? fileList[0].name : t('SystemConfig.Update.NoFileSelected') }}</span>
        </ElUpload>
        <div style="flex: 1;" />
        <VBtn
          color="primary"
          variant="tonal"
          class="mr-2"
          @click="backUp"
        >
          <VIcon
            icon="tabler-download"
            class="mr-1"
          />
          {{ t('SystemConfig.Update.BackupConfig') }}
        </VBtn>
        <VBtn
          color="primary"
          variant="tonal"
          @click="importBackUp"
        >
          <VIcon
            icon="tabler-upload"
            class="mr-1"
          />
          {{ t('SystemConfig.Update.ImportBackup') }}
        </VBtn>
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
.bg-secondary-opacity {
  background: rgba($color: var(--v-theme-secondary), $alpha: 8%);
}

.bg-primary-semi-transparent {
  background: rgba($color: var(--v-theme-primary), $alpha: 8%);
}
</style>
