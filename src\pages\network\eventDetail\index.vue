<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import moment from 'moment/moment'
import { useI18n } from 'vue-i18n'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const { t } = useI18n()

const EVENT_STATUS_LIST = computed(() => {
  return EVENT_STATUS.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const productsData = ref({
  total: 3,
  products: [],
})

// 添加分页处理计算属性
const paginatedProducts = computed(() => {
  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return productsData.value.products.slice(start, end)
})

const headers = [
  { title: t('NetworkEventDetail.LastTriggerTime'), key: 'timestamp' },
  { title: `${t('NetworkEventDetail.DeviceName')}[MAC]`, key: 'user_name' },
  { title: t('NetworkEventDetail.Model'), key: 'model' },
  { title: t('NetworkEventDetail.AffectedTerminalCount'), key: 'client_users' },
  { title: t('NetworkEventDetail.EventStatus'), key: 'status' },
  { title: t('NetworkEventDetail.Detail'), key: 'detail' },
]

const totalProduct = computed(() => {
  if (eventType.value === -1)
    return originalData.value.length

  return originalData.value.filter(item => item.status == eventType.value).length || 0
})

const page = ref(1)
const itemsPerPage = ref(10)

const eventType = ref(-1)

const route = useRoute()
let event_id = ''
onMounted(() => {
  route.query.event_id && (event_id = route.query.event_id as string)
  getEventList()
})

const originalData = ref([])

const activeEvent = ref({})

const totalDeal = ref(0)

const getEventList = () => {
  $api('', { requestType: 522 }).then(result => {
    if (result.err_code === 0) {
      const list = result.info.ap_events
      const arr = list.filter(item => item.event_id === event_id)
      const mianInfo = arr[0]

      originalData.value = list.filter(item => item.event_name === mianInfo.event_name)
      productsData.value = {
        ...productsData.value,
        products: originalData.value,
        total: originalData.value.length,
      }
      activeEvent.value = productsData.value.products[0]
      totalDeal.value = productsData.value.products.filter(item => item.status == 0).length
      console.log('getEventList', originalData.value)
    }
  })
}

const markResolved = (item: any) => {
  $api('', {
    requestType: 524,
    data: {
      event_id: item.event_id,
      status: '2',
    },
  }).then((res => {
    if (res.err_code === 0)
      getEventList()
  }))
}

const markIgnored = (item: any) => {
  $api('', {
    requestType: 524,
    data: {
      event_id: item.event_id,
      status: '1',
    },
  }).then((res => {
    if (res.err_code === 0)
      getEventList()
  }))
}

const router = useRouter()

const toAP = (item: any) => {
  router.push({
    name: 'network-device-detail',
    query: {
      mac: item.mac,
    },
  })
}

watch(
  [() => eventType.value, () => originalData.value],
  ([newEventType, newOriginalData]) => {
    console.log('filter', newEventType, newOriginalData)
    let filteredProducts = []
    if (newEventType == -1) {
      filteredProducts = newOriginalData
    }
    else {
      filteredProducts = newOriginalData.filter(product => {
        console.log(999, newEventType)

        return product.status == newEventType
      })
    }
    console.log('筛选条件变化，当前条件:', eventType.value)
    console.log('筛选后数据长度:', filteredProducts.length)
    console.log('更新前分页总数:', productsData.value.total)
    productsData.value = {
      ...productsData.value,
      products: filteredProducts,
      total: filteredProducts.length,
    }
  },
  { immediate: true, deep: true },
)

// 监听事件类型变化，重置分页到第一页
watch(eventType, () => {
  page.value = 1
})

const eventClass = {
  0: 'default',
  1: 'info',
  2: 'warning',
  3: 'error',
}

const getEventIndex = (item: any) => {
  const str = item.severity

  // Critical/High/Medium/Minor
  if (str == 'Critical')
    return 3
  else if (str == 'High')
    return 2
  else if (str == 'Medium')
    return 1
  else if (str == 'Minor')
    return 0
}

const getEventName = (item: any) => {
  const index = getEventIndex(item)
  if (index >= 0 && index < NETWORK_EVENT_LEVEL.length)
    return t(NETWORK_EVENT_LEVEL[index].title)

  return '未知'
}

const formatTime = (time: string) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

const drawer = ref(false)
const eventDetail = ref({})
const deviceInfo = ref({})

const viewDetail = (item: any) => {
  $api('', {
    requestType: 530,
    data: { mac: item.mac },
  }).then((res => {
    if (res.err_code === 0)
      deviceInfo.value = res.info
    else
      ElMessage.error('获取设备信息失败')
  }))
  drawer.value = true
  eventDetail.value = item
}
</script>

<template>
  <div class="event-detail">
    <VCard class="mb-6">
      <template #title>
        <div class="mr-2 text-h5">
          <div v-if="activeEvent.event_name == '0'">
            {{ t('NetworkEventDetail.APOnline') }}
          </div>
          <div v-if="activeEvent.event_name == '1'">
            {{ t('NetworkEventDetail.APOffline') }}
          </div>
          <div v-if="activeEvent.event_name == '3'">
            {{ t('NetworkEventDetail.APHighTemp') }}
          </div>
        </div>
      </template>
      <VCardText>
        <VRow>
          <VCol cols="3">
            <div class="label">
              {{ t('NetworkEventDetail.Type') }}
            </div>
            <div class="value">
              <div v-if="activeEvent.type == '0'">
                {{ t('NetworkEventDetail.DeviceAccess') }}
              </div>
              <div v-if="activeEvent.type == '1'">
                {{ t('NetworkEventDetail.DevicePerformance') }}
              </div>
            </div>
          </VCol>
          <VCol cols="3">
            <div class="label">
              {{ t('NetworkEventDetail.Level') }}
            </div>
            <div class="value">
              <VChip
                variant="outlined"
                :color="eventClass[getEventIndex(activeEvent)]"
              >
                {{ getEventName(activeEvent) }}
              </VChip>
            </div>
          </VCol>
          <VCol cols="3">
            <div class="label">
              {{ t('NetworkEventDetail.Count') }}
            </div>
            <div class="value">
              0
            </div>
          </VCol>
          <VCol cols="3">
            <div class="label">
              {{ t('NetworkEventDetail.AffectedDeviceCount') }}
            </div>
            <div class="value">
              0
            </div>
          </VCol>
        </VRow>
        <VRow>
          <VCol cols="3">
            <div class="label">
              {{ t('NetworkEventDetail.AffectedTerminalCount') }}
            </div>
            <div class="value">
              {{ activeEvent.client_users }}
            </div>
          </VCol>
          <VCol cols="3">
            <div class="label">
              {{ t('NetworkEventDetail.LastTriggerTime') }}
            </div>
            <div class="value">
              {{ formatTime(activeEvent.timestamp) }}
            </div>
          </VCol>
          <VCol cols="3">
            <div class="label">
              {{ t('NetworkEventDetail.PendingEventCount') }}
            </div>
            <div class="value">
              {{ totalDeal }}
            </div>
          </VCol>
          <VCol cols="3">
            <!--            <div class="label">操作</div> -->
            <!--            <div class="value"> -->
            <!--              <span class="text-primary text-button cursor-pointer">不在提醒</span> -->
            <!--            </div> -->
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <VCard>
      <template #title>
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <div class="mr-4 text-h5">
              {{ t('NetworkEventDetail.NetworkEventCount') }}
            </div>
            <!--            <VChip color="success"> -->
            <!--              2 -->
            <!--            </VChip> -->
          </div>
          <div>
            <BtnGroupSelector
              v-model:value="eventType"
              :options="EVENT_STATUS_LIST"
              item-title="label"
            />
          </div>
        </div>
      </template>

      <VDivider />

      <VDataTableServer
        :items="paginatedProducts"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text=" t('NoData') "
        disable-sort
      >
        <template #item.user_name="{ item }">
          <div @click="toAP(item)">
            <div class="text-primary">
              {{ item.user_name }}
            </div>
            <div class="text-primary">
              {{ item.mac }}
            </div>
          </div>
        </template>
        <template #item.timestamp="{ item }">
          {{ formatTime(item.timestamp) }}
        </template>
        <template #item.status="{ item }">
          <div v-if="item.status == '0'">
            {{ t('NetworkEventDetail.Pending') }}
          </div>
          <div v-if="item.status == '1'">
            {{ t('NetworkEventDetail.Ignored') }}
          </div>
          <div v-if="item.status == '2'">
            {{ t('NetworkEventDetail.Processed') }}
          </div>
        </template>
        <template #item.detail="{ item }">
          <VBtn
            v-if="item.status == '0'"
            variant="text"
            @click="markResolved(item)"
          >
            {{ t('NetworkEventDetail.MarkAsResolved') }}
          </VBtn>
          <VBtn
            v-if="item.status == '0'"
            variant="text"
            @click="markIgnored(item)"
          >
            {{ t('NetworkEventDetail.Ignore') }}
          </VBtn>
          <VBtn
            variant="text"
            @click="viewDetail(item)"
          >
            {{ t('NetworkEventDetail.Detail') }}
          </VBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            v-model:items-per-page="itemsPerPage"
            :total-items="totalProduct"
            show-meta
          />
        </template>
      </VDataTableServer>
    </VCard>
    <!-- 事件详情 -->
    <VNavigationDrawer
      v-if="drawer"
      v-model="drawer"
      persistent
      temporary
      location="right"
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <div class="flex-shrink-0	 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ t('NetworkEventDetail.EventDetails') }}
          </div>
          <VBtn
            icon
            variant="text"
            color="medium-emphasis"
            size="small"
            @click="drawer = false"
          >
            <VIcon
              icon="tabler-x"
              color="high-emphasis"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <PerfectScrollbar
          class="pa-4 flex-1-0"
          tag="ul"
          :options="{ wheelPropagation: false }"
        >
          <div class="text-h5 mb-4 d-flex">
            {{ deviceInfo.model }} :
            <div v-if="eventDetail.event_name === '0'">
              {{ t('NetworkEvent.EventNames.APOnline') }}
            </div>
            <div v-if="eventDetail.event_name === '1'">
              {{ t('NetworkEvent.EventNames.APOffline') }}
            </div>
            <div v-if="eventDetail.event_name === '3'">
              {{ t('NetworkEvent.EventNames.APHighTemp') }}
            </div>
          </div>
          <div class="bg-grey-light pa-4 mb-4 rounded">
            <div class="text-button mb-2">
              {{ t('NetworkEventDetail.BasicInfo') }}
            </div>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.Type') }}
                </div>
                <div class="value">
                  <div v-if="eventDetail.type == '0'">
                    {{ t('NetworkEventDetail.DeviceAccess') }}
                  </div>
                  <div v-if="eventDetail.type == '1'">
                    {{ t('NetworkEventDetail.DevicePerformance') }}
                  </div>
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.Level') }}
                </div>
                <div class="value">
                  <VChip
                    variant="outlined"
                    :color="eventClass[getEventIndex(eventDetail)]"
                  >
                    {{ getEventName(eventDetail) }}
                  </VChip>
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkEvent.OccurrenceTime') }}
                </div>
                <div class="value">
                  {{ formatTime(eventDetail.timestamp) }}
                </div>
              </VCol>
            </VRow>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.EventDescription') }}
                </div>
                <div v-if="eventDetail.event_name === '0'">
                  {{ eventDetail.description || t('NetworkEvent.EventNames.APOnline') }}
                </div>
                <div v-if="eventDetail.event_name === '1'">
                  {{ eventDetail.description || t('NetworkEvent.EventNames.APOffline') }}
                </div>
                <div v-if="eventDetail.event_name === '3'">
                  {{ eventDetail.description || t('NetworkEvent.EventNames.APHighTemp') }}
                </div>
              </VCol>
            </VRow>
          </div>

          <div class="bg-grey-light pa-4 mb-4 rounded">
            <div class="text-button mb-2">
              {{ t('NetworkEvent.DeviceInfo') }}
            </div>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.DeviceName') }}
                </div>
                <div class="value">
                  {{ deviceInfo.usr_name }}
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.Status') }}
                </div>
                <div
                  v-if="deviceInfo.onoff == 'online'"
                  class="value text-success"
                >
                  {{ t('NetworkEventDetail.Online') }}
                </div>
                <div
                  v-else
                  class="value text-error"
                >
                  {{ t('NetworkEventDetail.Offline') }}
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.Version') }}
                </div>
                <div class="value">
                  {{ deviceInfo.version }}
                </div>
              </VCol>
            </VRow>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.IP') }}
                </div>
                <div class="value">
                  {{ deviceInfo.ip }}
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.MAC') }}
                </div>
                <div class="value">
                  {{ deviceInfo.mac }}
                </div>
              </VCol>
              <VCol />
            </VRow>
          </div>

          <div class="rounded pa-4 border border-sm">
            <div class="text-button mb-2">
              {{ t('NetworkEventDetail.Maintenance') }}
            </div>
            <div class="text-on-surface opacity-60 text-body-1">
              {{ t('NetworkEventDetail.MaintenanceDesc') }}
            </div>
          </div>
        </PerfectScrollbar>
        <VDivider />
        <div class="flex-shrink-0	 pa-4 d-flex align-center justify-end ">
          <VBtn
            class="mr-4"
            color="secondary"
            variant="tonal"
            @click="drawer = false"
          >
            {{ t('NetworkEventDetail.Cancel') }}
          </VBtn>
          <VBtn
            v-if="eventDetail.status == '0'"
            color="primary"
            @click="markResolved(eventDetail)"
          >
            {{ t('NetworkEventDetail.MarkAsResolved') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style lang="scss" scoped>
.event-detail {
  .label {
    color: #999;
    font-size: 13px;
    margin-block-end: 2px;
  }

  .value {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
    font-size: 15px;
  }
}
</style>
