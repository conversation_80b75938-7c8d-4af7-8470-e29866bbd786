{"Dashboard": "仪表盘", "SystemManagement": "系统管理", "StatusMonitoring": "状态监控", "NetworkConfiguration": "网络配置", "SystemConfiguration": "系统配置", "WirelessOptimization": "无线调优", "DeviceManagement": "设备管理", "DeviceDiscovery": "设备发现", "APManagement": "AP管理", "ConfigurationManagement": "配置管理", "TemplateManagement": "模版管理", "APConfigurationDeployment": "AP配置下发", "NetworkManagement": "网络管理", "NetworkTopology": "网络拓扑", "NetworkDataStatistics": "网络数据统计", "APData": "AP数据", "TerminalData": "终端数据", "NetworkOverview": "网络概况", "NetworkStatus": "网络状态", "DeviceStatus": "设备状态", "NetworkOperations": "网络运维", "NetworkEvents": "网络事件", "WelcomeMessage": "欢迎使用", "Someone": "有人", "IoT": "有人物联", "CommercialNetwork": "商用网络", "SimplifyComplexity": "化繁为简", "EasyControl": "轻松掌控您的网络系统", "Username": "账户名", "Password": "密码", "RememberAccount": "记住账号", "Login": "登录", "Tip": {"EnterPassword": "请输入密码", "EnterAccount": "请输入账号", "errorStr": "所选设备型号不一致，请选择相同型号的设备或先选择型号"}, "PleaseSelect": "请选择", "Unknown": "未知", "BandwidthUsage": "带宽使用量", "TotalUsageAllDevices": "所有设备的总使用量", "Notifications": "事件通知", "NewNotifications": "条新通知", "APOnline": "AP上线", "APOffline": "AP离线", "APHighTemperature": "AP高温事件", "IgnoreAlert": "忽略告警", "Ignored": "已忽略", "NoEvents": "暂无事件", "HighTemperatureWarning": "CPU/WiFi2G/WiFi5G其中一个达到超过100度生成报警事件", "Logout": "退出", "5": "5", "10": "10", "20": "20", "25": "25", "50": "50", "100": "100", "$vuetify": {"badge": "Badge", "loading": "Chargement", "noDataText": "Pas de données disponibles", "close": "<PERSON><PERSON><PERSON>", "open": "Ouvert", "carousel": {"ariaLabel": {"delimiter": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "dataFooter": {"itemsPerPageText": "Objets par page:", "itemsPerPageAll": "<PERSON>ut", "pageText": "{0}-{1} of {2}", "firstPage": "Première page", "prevPage": "<PERSON> p<PERSON>", "nextPage": "<PERSON> suivante", "lastPage": "Dernière page"}, "pagination": {"ariaLabel": {"root": "racine", "previous": "précédente", "first": "d'abord", "last": "<PERSON><PERSON><PERSON>", "next": "suivante", "currentPage": "page actuelle", "page": "page"}}, "input": {"clear": "dégager", "appendAction": "ajouter une action", "prependAction": "préfixer l'action", "counterSize": "counterSize", "otp": "otp"}, "fileInput": {"counterSize": "<PERSON><PERSON> du compteur"}, "rating": {"ariaLabel": {"item": "Objet"}}}, "OnlineAP": "在线AP", "OfflineAP": "离线AP", "TerminalCount": "终端数量", "UnitDevice": "台", "AC": "AC", "AP": "AP", "Router": "路由器", "Offline": "离线", "Name": "名称", "IPAddress": "IP地址", "AccessType": "连接方式", "Signal": "信号", "LeaseRemaining": "剩余租期", "HistoryTraffic": "流量", "All": "全部", "OnlineClients": "在线客户端", "NoData": "暂无数据", "DeviceLoadRanking": "设备负载排行", "Icon": "图标", "Device1": "设备1", "Terminal": "终端", "AlarmList": "告警列表", "Content": "内容", "DeviceName": "设备名称", "Type": "类型", "Time": "时间", "DeviceOffline": "设备离线", "EventType1": "事件类型 1", "TodayAlarm": "今日告警", "CriticalEvent": "紧急事件", "HighEvent": "高级事件", "MediumEvent": "中级事件", "LowEvent": "低级事件", "DeviceEventCount": "设备发生事件次数", "View": "查看", "RouterTotalRate": "路由总速率", "Upload": "上传", "Download": "下载", "CountUnit": "个", "DataFetchFailed": "获取数据失败", "RequestError": "请求错误", "SystemStatus": {"Title": "系统状态", "CurrentSettings": "当前系统设置", "Hostname": "主机名", "Model": "型号", "FirmwareVersion": "固件版本", "IPAddress": "IP地址", "RunningTime": "运行时间", "MACAddress": "MAC地址", "Days": "天", "UsageRate": "使用率", "APCount": "AP数量", "SerialNumber": "序列号", "NetworkAccess": "接入网络"}, "NetworkTraffic": {"Title": "网络流量", "Subtitle": "实时监控带宽占用", "Upload": "上传", "Download": "下载", "Units": {"BytesPerSecond": "B/秒", "KBPerSecond": "KB/秒", "MBPerSecond": "MB/秒", "GBPerSecond": "GB/秒"}}, "CPUUsage": {"Title": "CPU使用率", "Subtitle": "实时监控CPU负载"}, "MemoryUsage": {"Title": "内存使用率", "Subtitle": "实时监控内存占用"}, "NoInternet": "无 Internet", "Internet": "Internet", "Local": "本地", "NetworkConfig": {"Tabs": {"PortStatus": "网口状态", "NetworkSettings": "网络设置", "IPMapping": "IP映射", "PortMapping": "端口映射", "LANSettings": "LAN设置", "DHCPIPBinding": "绑定DHCP IP"}, "Modes": {"RouterMode": "路由模式", "APMode": "AP模式", "RelayMode": "中继模式"}, "LAN": {"GatewayIP": "网关IP", "EnterGatewayIP": "请输入网关IP", "SubnetMask": "子网掩码", "EnterSubnetMask": "请输入子网掩码", "DHCPService": "DHCP服务", "EnableDHCPDesc": "启用DHCP服务，自动为连接的设备分配IP地址", "StartValue": "起始值", "EnterStartValue": "请输入起始值", "StartIPDesc": "DHCP分配的起始IP地址后缀", "MaxCount": "最大数量", "EnterMaxCount": "请输入最大数量", "MaxCountDesc": "最大可分配IP数量，最大数量是", "MaxCountError": "超过最大可分配数量，请修改起始值", "LeaseTime": "租期", "EnterLeaseTime": "请输入租期，如：2h", "LeaseTimeDesc": "DHCP分配的IP地址租期", "LeaseTimeMinimum": "租期必须大于2分钟(2m)", "LeaseTimeFormat": "租期格式不正确，请使用数字加单位，如：30m", "LeaseTimeFormatHelp": "支持格式：数字+单位，单位：m(分钟)、h(小时)、d(天)，如：30m、12h、1d", "SaveSettings": "保存设置", "Required": "此项为必填项", "MaxValueExceeded": "最大值不能超过", "MaxValueExceededA": "最大数量为", "MaxValueExceededB": ",请修改", "EnterInteger": "请输入整数", "RangeLimit": "请输入1-6之间的整数"}, "DHCP": {"Title": "DHCP IP绑定", "AddDevice": "添加设备", "AddDHCPBinding": "添加DHCP IP绑定", "DeviceName": "设备名称", "DeviceNamePlaceholder": "请输入英文、数字或符号作为设备名称，暂不支持中文输入。", "IPAddress": "IP地址", "IPAddressPlaceholder": "请输入标准 IPv4 地址，格式如 *************。", "MACAddress": "MAC 地址", "MACAddressPlaceholder": "请输入标准 MAC 地址，格式如 01:23:45:67:89:AB。", "Add": "添加", "Cancel": "取消", "InvalidIPv4": "请输入有效的IPv4地址", "InvalidMAC": "请输入有效的MAC地址", "InvalidDeviceName": "仅允许英文字母、数字、!@#$%-_+=，最长32个字符，不允许空格和中文", "DHCPBindingDesc": "当您为 LAN 内的客户端指定预留 IP 地址时，客户端每次访问路由器的 DHCP 服务器时会始终收到相同的 IP 地址。 您可以将预留的 IP 地址分配给需要永久 IP 设置的计算机或服务器。", "DHCPBindingNote": "注意，被配置的客户端必须重新连接路由器才能生效。", "DHCPBindingError": "请检查是否绑定了该IP地址或MAC地址"}, "WAN": {"Title": "网口详情", "SetAllLAN": "设为全部 LAN 口", "WanLanExchange": "WAN / LAN 交换", "Port": "端口", "Speed": "速率", "Send": "发送", "Receive": "接收", "SaveSettings": "保存设置", "Speeds": {"2500M": "2.5G", "1000M": "1000M", "10_100M": "10/100M"}, "NotConnected": "未连接"}, "Network": {"DynamicIP": "动态IP (DHCP)", "DynamicIPDesc": "设备自动获取上层服务器分配的IP地址、子网掩码、网关和DNS地址。", "PPPoE": "宽带拨号(PPPoE)", "PPPoEDesc": "请输入运营商（电信、联通、移动等）提供的用户名、密码，通过拨号方式连接网络。", "StaticIP": "静态IP地址", "StaticIPDesc": "请设置固定的WAN端IP地址、子网掩码、网关和DNS地址。", "MACClone": "MAC地址克隆", "MACCloneDesc": "模拟特定设备的MAC地址，以适应某些ISP的绑定要求。", "CurrentMAC": "当前MAC地址", "CloneMAC": "克隆MAC地址", "EnterCloneMAC": "请输入克隆MAC地址", "CloneMACRequired": "克隆MAC地址不能为空", "CloneMACInvalid": "克隆MAC地址格式不正确", "InternetAccount": "上网账号", "EnterInternetAccount": "请输入上网账号", "InternetAccountRequired": "上网账号不能为空", "InternetAccountInvalid": "上网账号格式不正确", "InternetPassword": "上网密码", "EnterInternetPassword": "请输入上网密码", "InternetPasswordRequired": "上网密码不能为空", "InternetPasswordInvalid": "密码格式不正确", "IPAddress": "IP地址", "EnterIPAddress": "请输入IP地址", "IPAddressRequired": "请输入IP地址", "IPAddressInvalid": "IP地址不正确", "Subnet": "子网掩码", "EnterSubnet": "请输入子网掩码", "SubnetRequired": "请输入子网掩码", "SubnetInvalid": "子网掩码不正确", "Gateway": "网关地址", "EnterGateway": "请输入网关地址", "GatewayRequired": "请输入网关地址", "GatewayInvalid": "网关地址不正确", "PrimaryDNS": "首选DNS服务器", "EnterPrimaryDNS": "请输入首选DNS服务器", "PrimaryDNSRequired": "请输入首选DNS服务器", "PrimaryDNSInvalid": "首选DNS服务器不正确", "SecondaryDNS": "备选DNS服务器", "EnterSecondaryDNS": "请输入备选DNS服务器", "SecondaryDNSRequired": "请输入备选DNS服务器", "SecondaryDNSInvalid": "备选DNS服务器不正确", "RecommendedDNS": "推荐使用阿里云DNS：********* ， *********", "SaveSettings": "保存设置", "AccountIncorrect": "上网账号不正确", "PasswordIncorrect": "上网密码不正确", "Error": "出错了，请重试", "RelayModeError": "出错了，请把系统模式设置为中继模式"}, "IP": {"Title": "IP映射", "AddIPMapping": "添加IP映射", "DeviceIP": "设备IP", "EnterDeviceIP": "请输入内网设备IP", "MappingIP": "映射IP", "EnterMappingIP": "请输入公网映射IP(通常为 AC WANIP)", "CommunicationHostIP": "通信主机IP（可选）", "EnterCommunicationHostIP": "请输入外部主机IP", "InternalIP": "内部IP", "EnterInternalIP": "系统自动分配IP", "Apply": "应用", "Close": "关闭", "Required": "请输入内容", "InvalidIP": "请输入有效的IP地址", "Description": "将内网设备的私有IP地址映射到公网IP，使外部设备可直接访问。", "Points": {"Point1": "将设备连接至 LAN 口，其 IP 地址会映射到 WAN 端，使外部设备可通过 映射 IP 直接访问该设备。", "Point2": "指定通信主机IP后，LAN 设备可以通过内部IP访问指定的WAN端主机，实现双向通信。", "Point3": "仅支持一个IP映射。", "Point4": "设备IP和映射IP不能和LAN/WAN网段冲突。"}}, "Port": {"Title": "端口映射", "AddPortMapping": "添加端口映射", "DeviceIP": "设备IP", "EnterDeviceIP": "请输入设备IP", "WANPCIP": "WAN端PCIP", "EnterWANPCIP": "请输入WAN端PCIP", "Port": "端口", "EnterPort": "请输入端口", "AddRule": "添加规则", "Status": "状态", "Actions": "操作", "Enable": "开启", "Disable": "关闭", "Delete": "删除", "Description": "典型应用场景：允许WAN口设备A通过路由器的WAN IP，访问LAN口下设备B的指定服务端口。", "Points": {"Point1": "必须先关闭 IP 映射，否则端口映射可能无效。", "Point2": "设备IP 应为 LAN网段 的IP，例如 **************。", "Point3": "WAN端PC IP 填入A设备IP（需与WAN同网段），如果WAN口未分配公网IP，可改用静态IP。", "Point4": "端口可以是 单个端口（如80） 或 连续端口范围（如8080-8090）。", "Point5": "配置后立即生效，A设备可直接通过 WAN IP+ 端口 访问B服务端口。"}, "Required": "此项为必填项", "InvalidIP": "请输入有效的IPv4地址", "InvalidPort": "请输入有效的端口", "Occupied": "端口已被占用", "InvalidPortRange": "端口范围无效"}}, "SystemConfig": {"Tabs": {"SystemSettings": "系统设置", "SecurityManagement": "安全管理", "UpgradeBackup": "升级与备份", "RestartDevice": "重启设备", "FactoryReset": "恢复出厂设置"}, "System": {"CurrentSystemTime": "当前系统时间", "SyncBrowserTime": "同步浏览器时间", "SyncNTPServerTime": "同步NTP服务器时间", "Timezone": "时区", "SelectTimezone": "请选择时区", "SaveSettings": "保存设置", "TimezoneSetSuccess": "时区设置成功", "BrowserTimeSyncSuccess": "浏览器时间同步成功"}, "Security": {"LoginSettings": "登录设置", "Username": "用户名称", "EnterUsername": "请输入用户名称", "USRCloud": "有人云", "USRCloudDesc": "设备将接入远程云管理服务，允许用户通过云端登录管理设备。远程管理功能需先在有人云平台注册并绑定设备。", "USRAcap": "AC管理", "USRAcapDesc": "控制AP管理权限。关闭后将无法管理AP，默认启用。", "OriginalPassword": "原密码", "EnterOriginalPassword": "请输入原密码", "NewPassword": "新密码", "NewPasswordDesc": "不能为空，不超过15个字符。建议使用字母+数字+特殊字符，提高安全性。", "ConfirmNewPassword": "确认新密码", "EnterNewPassword": "请输入新密码", "PleaseConfirmNewPassword": "请确认新密码", "SaveSettings": "保存设置", "PasswordMismatch": "两次输入的密码不一致", "SettingsSuccess": "设置成功", "IncorrectCredentials": "请输入正确的用户名和密码", "SamePassword": "出错了，请输入与原密码不相同的新密码", "SettingsFailed": "设置失败"}, "Restart": {"RestartDevice": "重启设备", "Warning": "警告", "RestartWarning": "重启设备将导致所有连接中断，请确保当前没有重要操作正在进行。", "RestartNow": "立即重启", "Cancel": "取消", "RestartSuccess": "重启成功", "FormatError": "格式错误", "UnknownRequestType": "未知的请求类型", "NotLoggedIn": "未登录", "SystemBusy": "系统忙", "RestartFailed": "重启失败"}, "Reset": {"FactoryReset": "恢复出厂设置", "Warning": "警告", "ResetWarning": "恢复出厂设置将清除所有配置信息，包括网络设置、用户密码等，设备将恢复到初始状态。此操作不可逆，请谨慎操作。", "ResetNow": "立即恢复出厂设置", "Cancel": "取消", "ResetSuccess": "重启成功", "FormatError": "格式错误", "UnknownRequestType": "未知的请求类型", "NotLoggedIn": "未登录", "SystemBusy": "系统忙", "ResetFailed": "重启失败"}, "Update": {"UpgradeMethod": "升级方式", "AutoUpgrade": "自动升级", "ManualUpgrade": "手动升级", "RestartAfterUpgrade": "下载完成后自动升级", "RestartAfterUpgradeDesc": "启用后，固件下载完成后将自动进行升级", "FirmwareUpgrade": "固件升级", "Desc": "刷写新的固件。点击浏览选择兼容的固件上传以刷新当前系统。若想升级过程中恢复出厂设置，请勾选恢复出厂设置选项，不勾选表示升级完成后系统保留当前配置信息。", "SelectFirmwareFile": "选择固件", "UploadFirmware": "开始更新", "PleaseUploadFirmware": "请上传固件文件", "UploadSuccess": "上传成功", "CurrentVersion": "当前版本", "LatestVersion": "最新版本", "UpgradeConfirmation": "检查更新", "Confirm": "确认", "Cancel": "取消", "UpgradeSuccess": "升级成功", "AlreadyLatestVersion": "当前版本已是最新版本", "FirmwareDownloaded": "固件已下载完成，是否重新下载？", "Yes": "是", "No": "否", "DownloadFailed": "固件下载失败", "ServerConnectionFailed": "服务器连接失败。", "FileVerificationError": "文件校验错误。", "GetFirmwareInfoFailed": "获取固件信息失败", "BackupRestore": "系统备份与恢复", "BackupRestoreDesc": "通过备份/恢复操作可以快速设置路由器的各项配置信息。", "ImportBackup": "开始恢复", "SelectFile": "选择文件", "NoFileSelected": "未选择任何文件", "BackupConfig": "备份配置", "ImportSuccess": "导入成功", "ClickUpdate": "点击安装", "DownloadIng": "下载中"}}, "Device": {"AP": {"GatewayMode": "网关模式", "BuiltInPage": "内置页面", "Title": "AP 管理", "Name": "名称", "DeviceName": "设备名称", "DeviceType": "设备型号", "DeviceTypeDesc": "设备类型", "Model": "型号", "SerialNumber": "SN", "IPAddress": "IP 地址", "MACAddress": "MAC 地址", "FirmwareVersion": "固件版本", "RunningTime": "运行时间", "Actions": "操作", "SelectModel": "选择型号", "SelectOperation": "请选择操作", "SelectAP": "请选择 AP", "Online": "在线", "Offline": "离线", "Operations": {"Restart": "重启", "FactoryReset": "恢复出厂设置", "Upgrade": "升级", "Delete": "删除", "BlinkLED": "闪烁 LED", "ExportSN": "导出序列号"}, "NoFileSelected": "未选择文件", "SelectFile": "选择文件", "RestoreFactory": "恢复出厂设置", "Blink": "闪烁", "Normal": "正常", "Confirm": "确认执行", "RemoteConfig": "远程配置", "DeviceManagement": "设备管理", "RestartDevice": "重启设备", "DeleteDevice": "删除设备", "NoSN": "没有序列号", "RestartConfirm": "确定要重启设备吗？", "DeleteConfirm": "确定要删除设备吗？", "Tip": "提示", "Cancel": "取消", "RestartSuccess": "重启成功", "RestartCancelled": "已取消重启", "DeleteSuccess": "删除成功", "DeleteCancelled": "已取消删除", "ExecuteSuccess": "执行成功", "UpgradeSuccess": "升级成功", "InternetStatus": "互联网状态", "WorkingMode": "工作模式", "RouterMode": "路由模式", "APMode": "AP模式", "LANIP": "LAN IP", "LAN1": "LAN1", "LAN2": "LAN2", "Connected": "已连接", "Disconnected": "未连接", "InterfaceRate": "接口速率", "NoDataYet": "暂无数据", "DeviceInfo": "设备信息", "WirelessStatus": "无线状态", "WorkMode": "工作模式", "RouterModeAPModeDesc": "工作模式切换指导", "RouterModeDesc": "路由模式：设备作为路由器使用，接入互联网", "APModeDesc": "AP模式：设备作为无线接入点使用", "SSID": "SSID", "EncryptionType": "加密类型", "Password": "密码", "Status": "状态", "APIsolation": "AP隔离", "Protocol": "协议", "CountryCode": "国家码", "Channel": "信道", "BandWidth": "频道带宽", "TransmitPower": "发射功率", "MaxConnections": "最大连接数", "Enable": "启用", "Disable": "停用", "Save": "保存", "ModeSwitchFailed": "模式切换失败", "EnterEightDigitPassword": "请输入至少8位的密码", "NoConfigNeeded": "无需配置", "ConfigSuccess": "配置成功", "VerificationTimeout": "验证超时", "DeployTimeout": "部署超时", "UnknownError": "未知错误", "GetStatusFailed": "获取状态失败", "All": "全部", "Required": "此项为必填项", "InvalidIPFormat": "请输入有效的IP地址格式", "IPRangeError": "IP地址每段必须在0-255之间", "IPLastByteError": "IP地址最后一段不能为0或255", "NotPrivateIP": "IP地址必须是私有网络地址(10.x.x.x、172.16-31.x.x、192.168.x.x)"}, "Remote": {"DeviceInfo": "设备信息", "Status": "状态", "DeviceType": "设备类型", "SoftwareVersion": "软件版本", "IPAddress": "IP地址", "MACAddress": "MAC地址", "OnlineTime": "上线时间", "RunningTime": "在线时间", "ModeSwitch": "模式切换", "RouterMode": "路由模式", "RouterModeDesc": "设置WAN口可以DHCP动态IP、静态IP、PPPOE拨号上网,LAN端自动分配IP", "APMode": "AP模式", "APModeDesc": "设置WAN、LAN桥接在一起,并且关闭自动分配IP", "GatewayMode": "网关模式", "GatewayModeDesc": "设备作为网关（第三层）工作，在不同网络之间进行转发。", "Cancel": "取消", "SaveSettings": "保存设置", "ModeSwitchFailed": "模式切换失败", "GetDeviceInfoFailed": "获取设备信息失败"}, "List": {"DeviceDiscovery": "设备发现", "ScanLocalNetwork": "扫描局域网设备", "BatchAddDevices": "批量添加设备", "Model": "型号", "SerialNumber": "序列号/SN", "IPAddress": "IP", "MACAddress": "MAC", "DiscoveryTime": "发现时间", "Status": "在线状态", "Actions": "操作", "Online": "在线", "Offline": "离线", "AddDevice": "添加设备"}}, "Config": {"Mode": {"BasicSettings": "基础设置", "TemplateManagement": "模版管理", "NewTemplate": "新建模版", "EditTemplate": "编辑模版", "CopyTemplate": "复制模版", "DeleteTemplate": "删除模版", "DeployConfig": "下发配置模板", "NewConfigTemplate": "新建配置模板", "DeleteConfirm": "确定删除模版 {name} 吗？", "Tips": "提示", "Confirm": "确定", "Cancel": "取消", "DeleteSuccess": "删除成功", "CopySuccess": "复制成功", "CopyFailed": "复制失败", "SaveSuccess": "保存成功", "SaveFailed": "保存失败", "PasswordLengthError": "请输入8位密码！", "SelectModel": "选择型号", "SSIDConfig": "SSID配置", "AdvancedConfig": "高级配置(可选)", "RoamingConfig": "漫游配置(可选)", "TemplateName": "模板名称", "EnterTemplateName": "请输入模板名称", "TemplateNameLengthError": "模板名称长度必须在1到64个字符之间", "Remark": "备注", "SelectRemark": "请选择备注", "RemarkLengthError": "备注长度必须在0到128个字符之间", "SelectAPModel": "选择AP型号", "Next": "下一步", "SaveTemplate": "保存模板", "None": "无", "EnterSSID": "请输入SSID", "SelectEncryption": "请选择加密类型", "EnterPassword": "请输入密码", "On": "开启", "Off": "关闭", "SelectProtocol": "请选择协议", "SelectCountry": "请选择国家", "SelectChannel": "请选择信道", "SelectBandwidth": "请选择信道带宽", "SelectTxPower": "请选择发射功率", "SelectNetworkType": "请选择网络类型", "SelectSpeedLimit": "请选择限速配置", "EnterUpstreamLimit": "请输入上行限制", "EnterDownstreamLimit": "请输入下行限制", "SelectRoamingProtocol": "请选择漫游协议", "SSIDLoadBalancing": "SSID负载均衡", "SSIDLoadBalancingPlaceholder": "整数，200ms或50～1000ms", "DisconnectWeakSignal": "断开弱信号终端", "DisconnectWeakSignalPlaceholder": "整数，-75dbm或-100～-50", "IgnoreWeakSignal": "忽略若信号探测", "IgnoreWeakSignalPlaceholder": "整数，-85dbm或-100～-50", "IgnoreExcessiveRetransmission": "忽略过度重传终端", "IgnoreExcessiveRetransmissionPlaceholder": "整数，50%或0～100", "NumericRangeValidation": "整数，50%或0～100", "Isolate": "隔离客户端", "Hide": "隐藏", "NetworkConfig": "网络配置", "NetworkType": "网络类型", "SpeedLimit": "限速配置", "UpstreamLimit": "上行限速", "DownstreamLimit": "下行限速", "QuickRoaming": "快速漫游", "RoamingProtocol": "漫游协议", "SSIDLengthError": "SSID长度必须在1到32个字符之间", "ApplicableModel": "适用型号", "Wi-Fi": "WI-FI", "CreationTime": "更新时间", "DeviceCount": "设备数量", "Description": "备注", "Actions": "操作", "GetTemplatesFailed": "获取模版列表失败", "WirelessSettings2G": "2.4G 无线设置", "WirelessSettings5G": "5G 无线设置", "SSID": "SSID名称", "EncryptionType": "加密类型", "Password": "密码", "Protocol": "协议", "Country": "国家码", "Channel": "信道", "Bandwidth": "信道带宽", "TxPower": "发射功率", "EnableWiFi": "启用Wi-Fi"}, "AP": {"DeviceName": "设备名称", "Model": "型号", "SerialNumber": "序列号/SN", "txPower.penetration": "穿墙", "txPower.normal": "普通", "txPower.saving": "节能", "IPAddress": "IP地址", "MACAddress": "MAC地址", "Status": "在线状态", "SelectDevices": "请选择设备", "SelectSameModel": "请选择相同型号的设备", "TemplateManagement": "模板管理", "NewTemplate": "新建模板", "EditTemplate": "编辑模板", "NewConfigTemplate": "新建配置模板", "DualBandUnify": "双频合一", "DualBandUnifyHint": "(2.4G/5G合并。让手机优先连接5G)", "TemplateName": "模板名称", "EnterTemplateName": "请输入模板名称", "TemplateNameLengthError": "模板名称长度必须在1到64个字符之间", "Remark": "备注", "SelectRemark": "请选择备注", "RemarkLengthError": "备注长度必须在0到128个字符之间", "SelectAPModel": "选择AP型号", "EnterAPModel": "请选择AP型号", "EnterSSID": "请输入SSID", "SSIDLengthError": "SSID长度必须在1到32个字符之间", "SelectEncryption": "请选择加密类型", "EnterPassword": "请输入密码", "PasswordLengthError": "请输入8位密码！", "On": "开启", "Off": "关闭", "Isolate": "隔离客户端", "SelectProtocol": "请选择协议", "SelectCountry": "请选择国家", "SelectChannel": "请选择信道", "SelectBandwidth": "请选择信道带宽", "SelectTxPower": "请选择发射功率", "NetworkType": "网络类型", "SelectNetworkType": "请选择网络类型", "SpeedLimit": "限速", "SelectSpeedLimit": "请选择限速配置", "UpstreamLimit": "上行限速", "EnterUpstreamLimit": "请输入上行限速", "DownstreamLimit": "下行限速", "EnterDownstreamLimit": "请输入下行限速", "QuickRoaming": "快速漫游", "RoamingProtocol": "漫游协议", "SelectRoamingProtocol": "请选择漫游协议", "WirelessSettings2G": "2.4G无线设置", "WirelessSettings5G": "5G无线设置", "SelectModel": "选择型号", "SSIDConfig": "SSID配置", "AdvancedConfig": "高级配置(可选)", "RoamingConfig": "漫游配置(可选)", "Next": "下一步", "SaveTemplate": "保存模板", "Cancel": "取消", "SaveSuccess": "保存成功", "SaveFailed": "保存失败", "SearchDevice": "请输入设备名称", "SelectPreConfigTemplate": "选择预配置模板", "SelectDistributionMethod": "选择下发方式", "SelectDistributionTime": "选择下发时间", "YearMonthDayTime": "年 / 月 / 日  --:--", "ConfigTimeout": "配置超时", "ConfigAllSuccess": "配置全部成功", "ConfigPartialSuccess": "配置部分成功", "RequestFailed": "请求失败", "ConfigSelection": "01 配置选择", "DistributionMethod": "02 下发方式", "CreateNewTemplate": "还没有模版？去创建", "ConfigDistributionComplete": "配置下发完成", "ConfigDistributing": "正在下发配置...", "TotalDistribution": "共下发", "Success": "成功", "Failed": "失败", "Online": "在线", "Offline": "离线", "All": "全部", "APConfigDistribution": "AP配置下发", "SendTemplate": "下发模板", "Previous": "上一步", "Confirm": "确认", "Complete": "完成", "ExportLog": "导出日志（CSV）", "ModelType": "型号的设备", "PleaseSelectModel": "请选择型号", "ConfigStatus": "配置状态", "ConfigTime": "配置时间", "NotStarted": "未开始", "ConfigDistributionRecord": "配置下发记录", "ConfirmDeploy": "确认下发", "DeployConfig": "下发配置模板"}, "Device": {"DeviceName": "设备名称", "DeviceType": "设备类型", "DeviceModel": "设备型号", "OnlineStatus": "在线状态", "Actions": "操作", "DeviceList": "设备列表", "DeviceTotal": "设备总数", "Online": "在线", "Offline": "离线", "DeviceDetails": "设备详情"}}, "Network": {"Tupo": {"NetworkTopology": "网络拓扑", "Internet": "互联网", "Terminal": "终端", "ExportTopology": "导出拓扑", "ExportAs": "导出为", "Export": "导出", "Cancel": "取消", "Type": "类型", "IPAddress": "IP地址", "MACAddress": "MAC地址", "RunningTime": "运行时长", "WiredConnection": "有线连接", "WirelessConnection": "无线连接", "PNGImage": "PNG图片", "PDFFile": "PDF文件", "JSONFile": "JSON文件", "CSVFile": "CSV文件", "Parent": "父级", "Name": "名称", "Level": "层级", "ID": "ID", "NotAvailable": "未获取到"}}, "Delete": "删除", "Action": "操作", "speedLimit": {"off": "关闭", "static": "静态限速"}, "template": {"immediate": "立即下发"}, "dateFilter": {"day": "1天", "week": "1周", "month": "1月", "year": "1年"}, "eventLevel": {"all": "全部", "notification": "通知", "minor": "轻微", "normal": "普通", "severe": "严重"}, "eventType": {"fault": "典型故障类", "load": "整机负载类"}, "terminal": {"online": "在线", "offline": "离线", "wired": "有线", "wireless": "无线"}, "event": {"unhandled": "未处理", "handled": "已处理", "ignored": "已忽略"}, "date": {"daily": "每天", "monday": "周一", "tuesday": "周二", "wednesday": "周三", "thursday": "周四", "friday": "周五", "saturday": "周六", "sunday": "周日"}, "txPower": {"penetration": "穿墙", "normal": "普通", "saving": "节能"}, "NetworkEvent": {"Title": "网络事件数量", "EventList": "事件列表", "EventLevel": "事件等级", "EventType": "事件类型", "EventName": "事件名称", "EventCount": "事件次数", "AffectedDeviceCount": "影响设备数", "AffectedTerminalCount": "影响终端数", "EventTime": "发生时间", "Details": "详情", "ViewDetails": "查看", "EventDetails": "事件详情", "BasicInfo": "基本信息", "Type": "类型", "Level": "等级", "OccurrenceTime": "发生时间", "EventDescription": "事件描述", "DeviceInfo": "设备信息", "DeviceName": "设备名称", "Status": "状态", "SoftwareVersion": "软件版本", "IPAddress": "IP地址", "MACAddress": "MAC地址", "MaintenanceSuggestions": "运维建议", "MaintenanceDesc": "定期统计终端接入数据，监控网络负载；正常时标记为已解决，无需额外处理", "Cancel": "取消", "MarkAsResolved": "标记已解决", "TotalEvents": "事件总数", "SevereEvents": "严重事件", "NormalEvents": "普通事件", "MinorEvents": "轻微事件", "NotificationEvents": "通知事件", "Severe": "严重", "Normal": "普通", "Minor": "轻微", "Notification": "通知", "DevicesWithMoreEvents": "事件较多设备", "TerminalsWithMoreEvents": "事件较多终端", "Online": "在线", "Offline": "离线", "DeviceTypes": {"AccessType": "设备接入类", "PerformanceType": "设备性能类"}, "EventNames": {"APOnline": "AP上线", "APOffline": "AP离线", "APHighTemp": "AP高温"}}, "NetworkEventDetail": {"APOnline": "AP上线", "APOffline": "AP离线", "APHighTemp": "AP高温", "Type": "类型", "DeviceAccess": "设备接入类", "DevicePerformance": "设备性能类", "Level": "等级", "Count": "次数", "AffectedDeviceCount": "影响设备数", "AffectedTerminalCount": "影响终端数", "LastTriggerTime": "最近触发时间", "PendingEventCount": "待处理事件数", "NetworkEventCount": "网络事件数量", "DeviceName": "设备名称", "Model": "设备型号", "Status": "状态", "EventStatus": "事件状态", "Online": "在线", "Offline": "离线", "Version": "软件版本", "IP": "IP地址", "MAC": "MAC地址", "BasicInfo": "基本信息", "EventDescription": "事件描述", "Maintenance": "运维建议", "MaintenanceDesc": "定期统计终端接入数据，监控网络负载；正常时标记为已解决，无需额外处理", "MarkAsResolved": "标记已解决", "Ignore": "忽略", "Detail": "详情", "EventDetails": "事件详情", "Cancel": "取消", "Processed": "已处理", "Ignored": "已忽略", "Pending": "待处理"}, "NetworkDeviceDetail": {"TerminalInfo": "终端信息", "Status": "状态", "Online": "在线", "Offline": "离线", "Model": "设备型号", "Version": "固件版本", "IPAddress": "IP地址", "MACAddress": "MAC地址", "Uptime": "上线时间", "Runtime": "在线时间", "CPUTitle": "CPU使用率", "CPUSubtitle": "实时监控CPU负载", "CPUAvg": "平均CPU利用率", "MemoryTitle": "内存使用率", "MemorySubtitle": "实时监控内存占用", "MemoryAvg": "平均内存利用率", "EventList": "事件列表", "NoMoreRemind": "不在提醒的事件", "EventLevel": "事件等级", "EventType": "事件类型", "EventName": "事件名称", "OccurrenceTime": "发生时间", "EventStatus": "事件状态", "Action": "操作", "DeviceAccess": "设备接入类", "DevicePerformance": "设备性能类", "APOnline": "AP上线", "APOffline": "AP离线", "APHighTemp": "AP高温", "Pending": "待处理", "Ignored": "已忽略", "Processed": "已处理", "MarkAsResolved": "标记已解决", "Ignore": "不在提醒", "Detail": "详情", "EventDetails": "事件详情", "BasicInfo": "基本信息", "EventDescription": "事件描述", "DeviceInfo": "设备信息", "Remove": "移除", "Maintenance": "运维建议", "MaintenanceDesc": "定期统计终端接入数据，监控网络负载；正常时标记为已解决，无需额外处理", "Cancel": "取消", "NO": "序号", "ConfirmRemove": "是否确认移除", "Tip": "提示", "Confirm": "确认", "NoMoreRemindEvents": "不在提醒的事件", "Type": "类型", "Level": "等级"}, "GetDeviceCPUStatisticsFailed": "获取设备CPU统计信息失败", "GetDeviceMemoryStatisticsFailed": "获取设备内存统计信息失败", "ApiErrors": {"PleaseLogin": "请先登录", "SetGetFailed": "设置/获取失败", "JSONFormatError": "JSON格式错误", "UnknownRequestType": "未知的请求类型", "LoginFailed": "登录失败", "InvalidValue": "无效的值", "InsufficientSpace": "空间不足", "GetInfoFailed": "获取信息失败", "NotLoggedIn": "未登录", "ImportConfigError": "导入配置文件出错", "ExportConfigError": "导出配置文件出错", "SystemBusyOtherRequest": "系统忙，正在运行其他请求", "ProcessingOtherRequest": "正在处理其他请求", "NetworkRestart": "网络重启", "SystemRestart": "系统重启", "UsernamePasswordError": "用户名或密码错误", "FileError": "文件错误", "RelaySettingError": "中继设置错误", "SamePassword": "新密码和原密码一致", "NetworkError": "网络错误"}, "TimeOut": "超时", "ServerFileName": "服务器文件名"}